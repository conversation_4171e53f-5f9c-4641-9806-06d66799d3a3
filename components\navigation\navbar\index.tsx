"use client";

// import Image from "next/image";
import React, { useState } from "react";
import { Menu, X, BellDot, Globe, ChevronDown } from "lucide-react";
// import { Button } from "@/components/ui/button";

const Navbar = ({ onToggleSidebar }: { onToggleSidebar?: () => void }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0">
      <div className="flex items-center justify-between">
        {/* Left side with menu button and logo */}
        <div className="flex items-center gap-3">
          {/* Sidebar toggle button for mobile/tablet */}
          <button
            onClick={onToggleSidebar}
            className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Toggle sidebar"
          >
            <Menu className="h-5 w-5 text-gray-600" />
          </button>

          <div className="text-lg sm:text-xl font-semibold text-gray-900">
            AI Interview
          </div>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center gap-4 xl:gap-6">
          {/* Notification Icon */}
          <BellDot className="h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors" />

          {/* Language Selector */}
          <div className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors">
            <Globe className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="font-bold hidden sm:inline">English</span>
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]" />
          </div>

          {/* Profile Section */}
          <div className="flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors">
            {/* Avatar */}
            <div className="h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-xs sm:text-sm font-medium text-gray-500 p-1"></span>
            </div>

            {/* User Info */}
            <div className="flex flex-col">
              <span className="text-xs sm:text-sm font-bold text-gray-900">
                Hammad M
              </span>
              <span className="text-xs text-purple-600">Free</span>
            </div>

            {/* Dropdown Arrow */}
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]" />
          </div>
        </div>

        {/* Tablet Navigation (768px - 1024px) */}
        <div className="hidden md:flex lg:hidden items-center gap-3">
          {/* Notification Icon */}
          <BellDot className="h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors" />

          {/* Compact Profile */}
          <div className="flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors">
            <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-xs font-medium text-gray-500">H</span>
            </div>
            <span className="text-sm font-bold text-gray-900">Hammad</span>
            <ChevronDown className="h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]" />
          </div>
        </div>

        {/* Mobile menu toggle - only on small screens */}
        <div className="sm:hidden">
          <button onClick={toggleMobileMenu} className="p-2">
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile dropdown - only on small screens */}
      {isMobileMenuOpen && (
        <div className="mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-500">H</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-bold text-gray-900">Hammad M</span>
              <span className="text-xs text-purple-600">Free</span>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <BellDot className="h-5 w-5 text-gray-700" />
            <span className="text-sm font-medium text-gray-700">
              Notifications
            </span>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <Globe className="h-5 w-5 text-gray-700" />
            <span className="text-sm font-medium text-gray-700">English</span>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
