import { MapPin, BriefcaseBusiness } from "lucide-react";

const JobInfoCard = () => {
  return (
    <div className="bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold mb-3">
            UX/UI Designer for Ai-Interview Web App
          </h2>
          <div className="flex gap-2 leading-relaxed mb-3 flex-wrap">
            <p className="text-sm text-gray-600 font-medium">
              $500 - $1000 <span className="font-extrabold px-1">·</span>
            </p>
            <div className="flex gap-1 items-center">
              <MapPin className="w-4 h-5" />
              <p className="text-sm text-gray-600 font-medium">New York</p>
            </div>
            <div className="flex gap-1 items-center">
              <BriefcaseBusiness className="w-4 h-5" />
              <p className="text-sm text-gray-600 font-medium">
                Onsite / Remote
              </p>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            We&apos;re building an AI-powered interview tool. We expect you to
            help users prepare by giving human interview experience generation.
          </p>
        </div>
        <span className="text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium">
          Active
        </span>
      </div>
    </div>
  );
};

export default JobInfoCard;
