"use client";
import React, { useState } from "react";
import InterviewInstructions from "./instructions/page";
import QuestionsPage from "./questions/page";
import InterviewRecording from "./recording/page";
import FinishInterview from "./finishInterview/page";

type InterviewStep =
  | "instructions"
  | "questions"
  | "recording"
  | "finishInterview";

const Interview = () => {
  const [currentStep, setCurrentStep] = useState<InterviewStep>("instructions");

  const renderCurrentComponent = () => {
    switch (currentStep) {
      case "instructions":
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
      case "questions":
        return <QuestionsPage onNext={() => setCurrentStep("recording")} />;
      case "recording":
        return (
          <InterviewRecording
            onNext={() => setCurrentStep("finishInterview")}
          />
        );

      case "finishInterview":
        return <FinishInterview />;
      default:
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
    }
  };

  return <div>{renderCurrentComponent()}</div>;
};

export default Interview;
